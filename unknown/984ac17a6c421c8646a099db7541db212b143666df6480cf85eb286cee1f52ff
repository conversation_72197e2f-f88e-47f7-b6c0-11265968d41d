import { Metadata } from 'next';

import { ProductWithDetails, Category, Subcategory } from '@/types/mysql-database';

// تعريف نوع Locale محلياً
type Locale = 'ar' | 'en';
import ResponsiveProductDetailPage from '@/components/ResponsiveProductDetailPage';
import Footer from '@/components/Footer';

interface ProductPageProps {
  params: Promise<{
    locale: Locale;
    id: string;
  }>;
}

// دالة لجلب بيانات المنتج من الخادم
async function fetchProductData(productId: string): Promise<{
  product: ProductWithDetails | null;
  category: Category | null;
  subcategory: Subcategory | null;
}> {
  try {
    console.log('🚀 Server: Fetching product details for ID:', productId);

    // جلب تفاصيل المنتج
    const productResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/products?id=${productId}`, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Server/1.0',
      },
      next: { revalidate: 300 }, // cache لمدة 5 دقائق
    });

    if (!productResponse.ok) {
      console.error(`❌ Server: Product API returned ${productResponse.status}`);
      return { product: null, category: null, subcategory: null };
    }

    const productResult = await productResponse.json();
    console.log('📦 Server: Product API response:', productResult);

    if (!productResult.success || !productResult.data) {
      console.error('❌ Server: Failed to fetch product details:', productResult);
      return { product: null, category: null, subcategory: null };
    }

    const product = productResult.data;
    let category: Category | null = null;
    let subcategory: Subcategory | null = null;

    // جلب بيانات الفئة الرئيسية
    if (product.category_id) {
      try {
        const categoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/categories?id=${product.category_id}`, {
          headers: { 'Content-Type': 'application/json' },
          next: { revalidate: 600 }, // cache لمدة 10 دقائق
        });
        if (categoriesResponse.ok) {
          const categoriesResult = await categoriesResponse.json();
          if (categoriesResult.success && categoriesResult.data) {
            category = categoriesResult.data;
          }
        }
      } catch (error) {
        console.error('❌ Server: Error fetching category:', error);
      }
    }

    // جلب بيانات الفئة الفرعية
    if (product.subcategory_id) {
      try {
        const subcategoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/subcategories?id=${product.subcategory_id}`, {
          headers: { 'Content-Type': 'application/json' },
          next: { revalidate: 600 }, // cache لمدة 10 دقائق
        });
        if (subcategoriesResponse.ok) {
          const subcategoriesResult = await subcategoriesResponse.json();
          if (subcategoriesResult.success && subcategoriesResult.data) {
            subcategory = subcategoriesResult.data;
          }
        }
      } catch (error) {
        console.error('❌ Server: Error fetching subcategory:', error);
      }
    }

    return { product, category, subcategory };
  } catch (error) {
    console.error('❌ Server: Error fetching product data:', error);
    return { product: null, category: null, subcategory: null };
  }
}

// دالة لإنشاء metadata للصفحة
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const id = resolvedParams?.id || '';

  const { product } = await fetchProductData(id);

  if (!product) {
    return {
      title: locale === 'ar' ? 'المنتج غير موجود' : 'Product Not Found',
      description: locale === 'ar' ? 'المنتج المطلوب غير موجود' : 'The requested product was not found',
    };
  }

  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  // إنشاء الرابط الكامل للمنتج
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://droobhajer.com';
  const productUrl = `${baseUrl}/${locale}/product/${id}`;

  // تحويل روابط الصور المحلية إلى روابط كاملة
  const productImages = product.images && product.images.length > 0
    ? product.images.map(img => ({
        url: img.image_url.startsWith('http') ? img.image_url : `${baseUrl}${img.image_url}`,
        width: 1200,
        height: 630,
        alt: productTitle,
      }))
    : [{
        url: `${baseUrl}/placeholder-image.jpg`,
        width: 1200,
        height: 630,
        alt: productTitle,
      }];

  return {
    title: `${productTitle} | ${locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer'}`,
    description: productDescription || (locale === 'ar' ? 'منتج عالي الجودة من دروب هاجر' : 'High quality product from Droob Hajer'),
    keywords: [
      productTitle,
      locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer',
      ...(locale === 'ar' ? [
        'أطباق بوفيه', 'أطباق عرض', 'أطباق تقديم فاخرة', 'معدات بوفيه',
        'أدوات الضيافة', 'تجهيزات فندقية', 'أطباق مطاعم', 'أطباق فنادق',
        'معدات عرض الطعام', 'أطباق تقديم احترافية', 'مستلزمات البوفيه',
        'تجهيزات المطاعم الفاخرة', 'أدوات تقديم الطعام', 'معدات الضيافة'
      ] : [
        'buffet plates', 'buffet display', 'buffet platters', 'buffet equipment',
        'buffet bowls', 'display plates', 'serving plates', 'hospitality equipment',
        'hotel supplies', 'restaurant equipment', 'catering supplies', 'food display',
        'professional serving', 'luxury tableware', 'buffet accessories', 'hotel buffet'
      ])
    ],
    authors: [{ name: locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer' }],
    openGraph: {
      title: productTitle,
      description: productDescription || (locale === 'ar' ? 'منتج عالي الجودة من دروب هاجر' : 'High quality product from Droob Hajer'),
      url: productUrl,
      siteName: locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer',
      images: productImages,
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: productTitle,
      description: productDescription || (locale === 'ar' ? 'منتج عالي الجودة من دروب هاجر' : 'High quality product from Droob Hajer'),
      images: productImages,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function ProductDetailPage({ params }: ProductPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const id = resolvedParams?.id || '';

  // جلب البيانات من الخادم
  const { product, category } = await fetchProductData(id);

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      <ResponsiveProductDetailPage
        locale={locale}
        initialProduct={product}
        initialCategory={category}
        initialSubcategory={null}
        productId={id}
      />
      <Footer locale={locale} />
    </div>
  );
}